{"permissions": {"allow": ["Bash(pip install:*)", "<PERSON><PERSON>(touch:*)", "Bash(docker buildx build:*)", "Bash(gcloud run deploy:*)", "<PERSON><PERSON>(docker push:*)", "<PERSON><PERSON>(docker buildx:*)", "Bash(docker build:*)", "Bash(docker system prune:*)", "<PERSON><PERSON>(gcloud auth:*)", "Bash(gcloud run services list:*)", "Bash(gcloud run services describe:*)", "Bash(docker tag:*)", "Bash(gcloud app deploy:*)", "<PERSON><PERSON>(curl:*)", "Bash(gcloud app logs read:*)", "Bash(gcloud app logs tail:*)", "Bash(gcloud config set:*)", "<PERSON><PERSON>(claude -c)", "Bash(gcloud builds submit:*)", "Bash(--image gcr.io/supergordasdeferrari/opus4:latest )", "<PERSON><PERSON>(--project supergordasdeferrari )", "Bash(--region us-central1 )", "Bash(--platform managed)", "Bash(--image gcr.io/supergordasdeferrari/opus4:latest )", "<PERSON><PERSON>(--project supergordasdeferrari )", "Bash(--region us-central1 )", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(pip show:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "<PERSON>sh(gcloud config:*)", "Bash(gcloud app:*)", "<PERSON><PERSON>(gcloud:*)", "Bash(./deploy.sh:*)", "Bash(./deploy-auto.sh)", "Bash(cp:*)", "Bash(pip index:*)"], "deny": []}}